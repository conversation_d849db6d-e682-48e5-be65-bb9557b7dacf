package utils

import (
	"testing"
)

func TestToChineseNumber(t *testing.T) {
	// 定义测试用例
	testCases := []struct {
		input    int
		expected string
	}{
		// 基本测试用例
		{0, ""},
		{-1, ""},
		{1, "一"},
		{2, "二"},
		{9, "九"},
		
		// 十位数测试
		{10, "十"},
		{11, "十一"},
		{19, "十九"},
		{20, "二十"},
		{21, "二十一"},
		{99, "九十九"},
		
		// 百位数测试
		{100, "一百"},
		{101, "一百零一"},
		{110, "一百一十"},
		{111, "一百一十一"},
		{199, "一百九十九"},
		{900, "九百"},
		{999, "九百九十九"},
		
		// 千位数测试
		{1000, "一千"},
		{1001, "一千零一"},
		{1010, "一千零一十"},
		{1011, "一千零一十一"},
		{1100, "一千一百"},
		{1101, "一千一百零一"},
		{1111, "一千一百一十一"},
		{9999, "九千九百九十九"},
		
		// 边界测试
		{10000, "10000"}, // 超出范围，返回阿拉伯数字
	}
	
	// 执行测试
	for _, tc := range testCases {
		t.Run("", func(t *testing.T) {
			result := ToChineseNumber(tc.input)
			if result != tc.expected {
				t.Errorf("ToChineseNumber(%d) = %s, expected %s", tc.input, result, tc.expected)
			}
		})
	}
}

// 测试特殊情况
func TestToChineseNumberSpecialCases(t *testing.T) {
	// 测试零的处理
	result := ToChineseNumber(0)
	if result != "" {
		t.Errorf("ToChineseNumber(0) = %s, expected \"\"", result)
	}
	
	// 测试负数的处理
	result = ToChineseNumber(-10)
	if result != "" {
		t.Errorf("ToChineseNumber(-10) = %s, expected \"\"", result)
	}
	
	// 测试超出范围的数字
	result = ToChineseNumber(10000)
	if result != "10000" {
		t.Errorf("ToChineseNumber(10000) = %s, expected \"10000\"", result)
	}
}

// 测试性能
func BenchmarkToChineseNumber(b *testing.B) {
	for i := 0; i < b.N; i++ {
		ToChineseNumber(1234)
	}
}
