package utils

import "fmt"

// 将阿拉伯数字转换为中文数字
// 支持 0 到 9999 的数字，超出范围返回阿拉伯数字
func ToChineseNumber(num int) string {
	if num <= 0 {
		return ""
	}

	if num > 9999 {
		return fmt.Sprintf("%d", num) // 超出范围时返回阿拉伯数字
	}

	// 基本数字
	chineseDigits := []string{"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"}
	// 进位单位
	chineseUnits := []string{"", "十", "百", "千"}

	// 分解数字的每一位
	digits := make([]int, 0)
	tempNum := num
	for tempNum > 0 {
		digits = append(digits, tempNum%10)
		tempNum /= 10
	}

	// 从高位到低位构建中文表示
	result := ""
	hasZero := false

	// 检查是否所有剩余位都是零
	allRemainingZero := func(pos int) bool {
		for j := pos - 1; j >= 0; j-- {
			if digits[j] != 0 {
				return false
			}
		}
		return true
	}

	for i := len(digits) - 1; i >= 0; i-- {
		digit := digits[i]

		if digit == 0 {
			// 处理零的情况
			if !hasZero && i > 0 && !allRemainingZero(i) {
				// 只有前面不是零，并且不是个位，且后面还有非零数字时才显示"零"
				result += "零"
				hasZero = true
			}
		} else {
			// 处理非零数字
			if digit == 1 && i == 1 && len(digits) == 2 {
				// 处理 10-19 的特殊情况，如"十一"而不是"一十一"
				result += chineseUnits[i]
			} else {
				result += chineseDigits[digit] + chineseUnits[i]
			}
			hasZero = false
		}
	}

	return result
}
