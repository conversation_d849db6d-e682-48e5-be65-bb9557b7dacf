package stallman

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

type StuexamReq struct {
	CourseId int64 `json:"courseId" form:"courseId"`
	Uid      int64 `json:"uid" form:"uid"`
}

type StuexamRsp struct {
	ExamId int64 `json:"examId"`
}

func init() {
	apis.Register(ApiStuexam, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  StuexamReq{},
		Encoder:  apis.EncoderForm,
		Response: StuexamRsp{},
	})
}
