package stallman

import (
	"assistantdeskgo/api/apis"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	ApiStuexam = "/stallman/ability/api/stuexam"
)

func Stuexam(ctx *gin.Context, req StuexamReq) (rsp StuexamRsp, err error) {
	err = apis.Do(ctx, req, &rsp)
	if err != nil {
		zlog.Warnf(ctx, "Stuexam failed, req: %+v, resp: %+v, err: %+v", req, rsp, err)
		return
	}
	return
}
