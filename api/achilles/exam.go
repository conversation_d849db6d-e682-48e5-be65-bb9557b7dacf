package achilles

import (
	"assistantdeskgo/api/apis"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	ApiCourseDetail = "/achilles/v3/origin/exam/ability/api/coursedetail"
)

func CourseDetail(ctx *gin.Context, req CourseDetailReq) (rsp CourseDetailRsp, err error) {
	err = apis.Do(ctx, req, &rsp)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseDetail failed, req: %+v, resp: %+v, err: %+v", req, rsp, err)
		return
	}
	return
}
