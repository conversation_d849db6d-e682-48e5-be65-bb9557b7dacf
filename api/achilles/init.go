package achilles

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

type CourseDetailReq struct {
	CourseId []string `json:"courseId" form:"courseId"`
}

type CourseDetailRsp struct {
	Details map[string]CourseDetailInfo `json:"details"`
}

type CourseDetailInfo struct {
	CourseId    int64        `json:"courseId"`
	EndTime     int64        `json:"endTime"`
	Interact    bool         `json:"interact"`
	BindExams   []BindExam   `json:"bindExams"`
	BindLessons []BindLesson `json:"bindLessons"`
}

type BindExam struct {
	ExamId  int64     `json:"examId"`
	TagList []TagInfo `json:"tagList"`
}

type BindLesson struct {
	LessonId int64     `json:"lessonId"`
	TagList  []TagInfo `json:"tagList"`
}

type TagInfo struct {
	Tid  int64    `json:"tid"`
	Tags []string `json:"tags"`
}

func init() {
	apis.Register(ApiCourseDetail, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  CourseDetailReq{},
		Encoder:  apis.EncoderJson,
		Response: CourseDetailRsp{},
	})
}
