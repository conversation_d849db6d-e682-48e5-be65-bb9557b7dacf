package deer

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtodeer"
	"assistantdeskgo/service/backend/deer"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func GetEvaluateTab(ctx *gin.Context) {
	param := dtodeer.GetDeerEvaluateTabReq{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	if param.CourseId <= 0 || param.StudentUid <= 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	result, err := deer.GetEvaluateTab(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func GetEvaluateStage2Report(ctx *gin.Context) {
	param := dtodeer.GetEvaluateStage2ReportReq{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	if param.CourseId <= 0 || param.StudentUid <= 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	result, err := deer.GetEvaluateStage2Report(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}
