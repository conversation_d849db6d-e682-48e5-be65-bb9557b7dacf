cos:
  bos:
    bucket: test-image
    cloud: baidu
    cnameEnabled: true
    directory: ""
    file_prefix: fudao_
    filesize_limit: 10485760000
    region: https://testimg.zuoyebang.cc
    secret_id: 80054b2251d24cb98eda994c93426d7d
    secret_key: 2dfba51842f2493d82339574941a5df2
  bos1:
    bucket: charge
    cloud: baidu
    cnameEnabled: true
    directory: ""
    file_prefix: fudao_
    filesize_limit: 1048576
    region: https://test-charge.bj.bcebos.com
    secret_id: 80054b2251d24cb98eda994c93426d7d
    secret_key: 2dfba51842f2493d82339574941a5df2

#  bos1:
#    bucket: charge
#    cloud: baidu
#    cnameEnabled: true
#    directory: ""
#    file_prefix: fudao_
#    filesize_limit: 1048576
#    region: https://charge.zuoyebang.cc
#    secret_id: 80054b2251d24cb98eda994c93426d7d
#    secret_key: 2dfba51842f2493d82339574941a5df2
  cos:
    bucket: zyb-image
    app_id: 1253445850
    secret_id: AKIDMUifMoX1kFTbx3uUnqc79qHSNo0wudOu
    secret_key: OW1OYS6V4QXZNFuKbfWAbNuBAsML4V9X
    region: ap-beijing
    picture_region: picbj
    filesize_limit: 2097152
    thumbnail: 1
    directory: ""
    file_prefix: fudao_
    cloud: tencent
    cdn: https://testimg.zuoyebang.cc
    
mysql:
  bzr:
    addr: cetus-common-svc.mysql:3306
    user: homework
    password: homework
    database: homework_fudao
    maxIdleTime: 300s
    maxidleconns: 50
    maxopenconns: 2
    readTimeOut: 3s
    service: evaluate
    writeTimeOut: 3s
    connMaxLifeTime: 3600s
    connTimeOut: 1500ms
  duxuesc:
    addr: cetus-zbfw-test-svc.mysql:3306
    user: homework
    password: homework
    database: homework_zhibo_duxuesc
    maxIdleTime: 300s
    maxidleconns: 50
    maxopenconns: 50
    readTimeOut: 3s
    service: duxuesc
    writeTimeOut: 3s
    connMaxLifeTime: 3600s
    connTimeOut: 1500ms
redis:
  assistantdeskgo:
    service: redis-fwyyevaluate
    addr: redis-qatest-svc.redis:6379
    maxIdle: 10
    maxActive: 200
    idleTimeout: 3s
    connTimeOut: 1s
    readTimeOut: 1s
    writeTimeOut: 1s

rmqv2:
  consumer:
    - broadcast: false
      group: support_assistantdeskgo_self_capture
      nameserver: ship-rocketmq-ceping-svc.mq:9876
      orderly: false
      retry: 3
      service: self_consume
      tags:
        - 214200
      topic: support_assistantdeskgo_fwyy-zb-core
    - service: "autoCallConsumer"
      nameserver: "ship-rocketmq-ceping-svc.mq:9876"
      topic: "support_assistantdesk_fwyy-zb-core"
      group: "support_assistantdeskgo_autocall"
      tags:
        - "217308"
      orderly: false
      retry: 10
      retry_interval: 10ms
    - service: "newLeadNotice"
      nameserver: "ship-rocketmq-ceping-svc.mq:9876"
      topic: "lpc_allocate_fwyy-zb-core"
      group: "support_assistantdeskgo_newleadnotice"
      tags:
        - "280022"
        - "280023"
      orderly: false
      retry: 3
      retry_interval: 10ms
    - service: "callrecordConsumer"
      nameserver: "ship-rocketmq-ceping-svc.mq:9876"
      topic: "support_muse_fwyy-zb-lpcmsg"
      group: "support_assistantdeskgo_callrecord"
      tags:
        - "271308"
      orderly: false
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: "audioresultConsumer"
      nameserver: "ship-rocketmq-ceping-svc.mq:9876"
      topic: "asr_realtime-quality-asr_speech-transcriberesult"
      group: "support_assistantdeskgo_audio_result"
      tags:
      orderly: false
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: "audioresultv2Consumer"
      nameserver: "ship-rocketmq-ceping-svc.mq:9876"
      topic: "asr_realtime-quality-asr-api_speech-transcriberesult"
      group: "support_assistantdeskgo_audio_result_v2"
      tags:
      orderly: false
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: "airesultConsumer"
      nameserver: "ship-rocketmq-ceping-svc.mq:9876"
      topic: "edu-aigc_aiturbo_aimishu"
      group: "support_assistantdeskgo_ai_result"
      tags:
      orderly: false
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: wxCallRecordConsumer
      nameserver: ship-rocketmq-ceping-svc.mq:9876
      topic: kunpeng-zb-kunpeng
      group: support_assistantdeskgo_wxcallrecord
      tags:
        - "182007"
      orderly: false
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: laxinTagFixImport
      nameserver: ship-rocketmq-ceping-svc.mq:9876
      topic: support_assistantdeskgo_fwyy-zb-core
      group: support_assistantdeskgo_laxin_tag_fix_import
      tags:
        - 214202
      orderly: true
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: aiAutoTag
      nameserver: ship-rocketmq-ceping-svc.mq:9876
      topic: edu-aigc_insight_trustee
      group: support_assistantdeskgo_sop_ai_banxue_tag
      tags:
        - "aiGenerateTag"
      orderly: true
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: KP182001-lpc
      nameserver: ship-rocketmq-ceping-svc.mq:9876
      topic: kunpeng-zb-kunpeng
      group: support_assistantdeskgo_kunpeng_182001_lpc
      tags:
        - "182001"
      orderly: true
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: KP182001
      nameserver: ship-rocketmq-ceping-svc.mq:9876
      topic: kunpeng-zb-kpteacher
      group: support_assistantdeskgo_kunpeng_182001
      tags:
        - "182001"
      orderly: true
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: touchLpcMsg
      nameserver: ship-rocketmq-ceping-svc.mq:9876
      topic: support_muse_fwyy-zb-lpcmsg
      group: support_assistantdeskgo_muselpcmsg_touch
      tags:
        - "271308"
      orderly: true
      retry: 3
      retry_interval: 10ms
      delay: 1s
    - service: onceTaskConsumer
      nameserver: ship-rocketmq-ceping-svc.mq:9876
      topic: support_delayer_delay-message
      group: support_assistantdeskgo_once_task
      tags:
        - "218108"
      orderly: false
      retry: 10
      retry_interval: 300ms
  producer:
    - nameserver: ship-rocketmq-ceping-svc.mq:9876
      retry: 1
      service: self_product
      topic: support_assistantdeskgo_fwyy-zb-core
    - service: "touchmisgoSopAIConfProducer"
      nameserver: "ship-rocketmq-ceping-svc.mq:9876"
      topic: "support_assistantdeskgo_fwyy-zb-touchnotify"
      retry: 3
      timeout: 1000ms

kafkapub:
  metrics:
    service: fwyy-data-assistant-metricscommon
    addr: "*************:9092,*************:9092,************:9092"
    version: 2.0.0


