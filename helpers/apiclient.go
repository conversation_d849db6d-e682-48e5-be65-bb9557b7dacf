package helpers

import (
	"assistantdeskgo/conf"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/apis"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/zbcore"
	"git.zuoyebang.cc/pkg/golib/v2/base"
)

var (
	ApiClientList = []apis.ApiClientInfo{
		{
			Roots:  apis.Duxuesc,
			Client: &conf.API.Duxuesc,
		},
		{
			Roots:  apis.Tower,
			Client: &conf.API.Tower,
		},
		{
			Roots:  apis.Allocate,
			Client: &conf.API.Allocate,
		},
		{
			Roots:  apis.Touchmisgo,
			Client: &conf.API.TouchmisGo,
		},
		{
			Roots:  apis.Exercise,
			Client: &conf.API.Exercise,
		},
		{
			Roots:  apis.ZbTikuApi,
			Client: &conf.API.ZbTikuApi,
		},
		{
			Roots:  apis.Delayer,
			Client: &conf.API.Delayer,
		},
		{
			Roots:  apis.MeshRoots,
			Client: &conf.API.Mesh,
		},
		{
			Roots:  apis.SuRoots,
			Client: &conf.API.Su,
		},
	}

	zbCoreClientList = map[string]*base.ApiClient{
		zbcore.ServiceTypeDal: &conf.API.ZbCoreDal,
		zbcore.ServiceTypeDau: &conf.API.ZbCoreDau,
		zbcore.ServiceTypeDat: &conf.API.ZbCoreDat,
		zbcore.ServiceTypeDas: &conf.API.ZbCoreDas,
	}
)

func InitApiClient() {
	conf.API.UserProfile.AppKey = "68DA5D3D7F2859B0"
	apis.RegisterApiClient(ApiClientList)
	err := zbcore.RegisterClient(zbCoreClientList)
	if err != nil {
		panic("zbCoreClient register error: %v" + err.Error())
	}
}
